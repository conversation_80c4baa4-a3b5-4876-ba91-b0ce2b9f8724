# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::TeacherContactsController, type: :controller do
  describe 'GET #index' do
    let(:organization) { current_organization }
    let(:user) { create(:user) }
    let(:teacher) { create(:user, name: 'Teacher Name', email: '<EMAIL>') }
    let(:course) { create(:course) }
    let!(:student_enrollment) do
      create(:enrollment, canvas_user_id: user.canvas_id, canvas_course_id: course.canvas_id, base_role_type: 'StudentEnrollment', workflow_state: 'active')
    end
    let!(:teacher_enrollment) do
      create(:enrollment, canvas_user_id: teacher.canvas_id, canvas_course_id: course.canvas_id, base_role_type: 'TeacherEnrollment', workflow_state: 'active')
    end

    let(:session) do
      PandaPal::Session.create(panda_pal_organization_id: organization.id,
                               data: {
                                 canvas_user_id: user.canvas_id,
                                 organization_key: organization.key
                               })
    end
    let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

    before do
      allow_any_instance_of(User).to receive(:against_shards).and_yield(user)
    end

    it 'returns teacher contacts for the user' do
      get :index, params: default_params.merge(user_id: user.canvas_id)
      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      expect(json['teacher_contacts']).to be_an(Array)
      expect(json['teacher_contacts'].first['canvas_id']).to eq(teacher.canvas_id)
      expect(json['teacher_contacts'].first['email']).to eq('<EMAIL>')
    end

    it 'returns empty array if no teachers found' do
      Enrollment.where(canvas_user_id: teacher.canvas_id).delete_all
      get :index, params: default_params.merge(user_id: user.canvas_id)
      json = JSON.parse(response.body)
      expect(json['teacher_contacts']).to eq([])
    end

    it 'returns 404 if user not found' do
      get :index, params: default_params.merge({ user_id: 999_999 })
      expect(response).to have_http_status(:not_found)
      json = JSON.parse(response.body)
      expect(json['error_message']).to eq('User not found')
    end
  end
end
