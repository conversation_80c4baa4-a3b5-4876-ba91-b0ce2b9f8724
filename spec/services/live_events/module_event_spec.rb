# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LiveEvents::ModuleEvent, type: :service do
  let(:payload) do
    {
      body: {
        context_type: 'Course',
        context_id: 12,
        module_id: 34
      }
    }.with_indifferent_access
  end

  let(:service) { described_class.new(payload) }
  let(:canvas_sync_client) { instance_double(Bearcat::Client) }
  let(:module_items) do
    [
      { id: 101, completion_requirement: { type: 'must_view', min_score: nil } },
      { id: 102 }
    ]
  end

  before do
    allow(service).to receive(:canvas_sync_client).and_return(canvas_sync_client)
    allow(canvas_sync_client).to receive_message_chain(:module_item, :all_pages!).and_return(module_items)
  end

  describe '#perform' do
    context 'when context_type is not Course' do
      before { payload[:body][:context_type] = 'Group' }

      it 'does not process the event' do
        expect(ContextModule).not_to receive(:find_or_initialize_by)
        service.perform(payload)
      end
    end

    context 'when context_type is Course' do
      let!(:course) { create(:course, canvas_id: payload[:body][:context_id]) }
      let!(:context_module) { create(:context_module, canvas_id: payload[:body][:module_id], canvas_context_id: course.canvas_id) }

      before do
        allow(ContextModule).to receive(:find_or_initialize_by).and_return(context_module)
        allow(ContextModuleItem).to receive(:find_or_initialize_by).and_return(instance_double(ContextModuleItem, sync_from_api: true))
        allow(context_module).to receive(:sync_from_api)
      end

      it 'sets completion requirements correctly' do
        expect(context_module).to receive(:sync_from_api)
        service.perform(payload)
      end
    end
  end
end
