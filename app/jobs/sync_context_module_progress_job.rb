# frozen_string_literal: true

class SyncContextModuleProgressJob < CanvasSync::Job
  queue_as :critical

  def perform(options = {})
    updated_after = extract_updated_after(options)
    context_modules = fetch_context_modules(updated_after)

    context_modules.each do |context_module|
      process_context_module(context_module)
    end
  end

  private

  def extract_updated_after(options)
    return unless options[:updated_after] == true

    last_batch = CanvasSync::SyncBatch.where(status: 'completed', batch_genre: 'default').last
    last_batch&.started_at&.iso8601
  end

  def fetch_context_modules(updated_after)
    if updated_after.present?
      ContextModule.where('updated_at < ?', updated_after)
    else
      ContextModule.all
    end
  end

  def process_context_module(context_module)
    return unless context_module.course

    user_ids = context_module.course.enrollments.only_with_k5_students.map(&:canvas_user_id)
    user_ids.each do |user_id|
      sync_student_module_progress(context_module, user_id)
    end
  rescue StandardError => e
    Rails.logger.error "Error syncing context module progress for #{context_module.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end

  def sync_student_module_progress(context_module, student_id)
    course_id = context_module.course.canvas_id
    module_id = context_module.canvas_id

    module_items = fetch_module_items(course_id, module_id, student_id)
    return unless module_items

    pages = fetch_pages(course_id)
    topics = fetch_topics(course_id)

    module_items.each do |item|
      todo_date = extract_todo_date(item, pages, topics)
      save_progression(module_id, item, student_id, todo_date)
    rescue StandardError => e
      Rails.logger.error "Error syncing context module progress for #{context_module.id}, item #{item['id']}, student #{student_id}: #{e.message}"
    end
  end

  def extract_todo_date(item, pages, topics)
    case item['type']
    when 'Page'
      page = pages&.find { |p| p['page_id'].to_s == item['content_id'].to_s || p['url'] == item['page_url'] }
      page&.dig('todo_date')
    when 'Discussion'
      topic = topics&.find { |t| t['id'].to_s == item['content_id'].to_s }
      topic&.dig('todo_date')
    end
  end

  def save_progression(module_id, item, student_id, todo_date)
    progression = ContextModuleProgression.find_or_initialize_by(
      canvas_module_id: module_id,
      canvas_module_item_id: item['id'],
      canvas_user_id: student_id
    )

    progression.assign_attributes(
      canvas_content_type: item['type'],
      canvas_content_id: item['content_id'],
      canvas_content_title: item['title'],
      canvas_page_url: item['page_url'],
      requirement_type: item.dig('completion_requirement', 'type'),
      requirement_status: item.dig('completion_requirement', 'completed') ? 'completed' : 'incomplete',
      lock_at: item.dig('content_details', 'lock_at'),
      due_at: item.dig('content_details', 'due_at'),
      todo_date: todo_date
    )

    progression.save! if progression.changed?
  end

  def fetch_module_items(course_id, module_id, student_id)
    canvas_sync_client.get(
      "/api/v1/courses/#{course_id}/modules/#{module_id}/items",
      include: %w[content_details],
      student_id: student_id
    ).all_pages!
  rescue Footrest::HttpError::NotFound => e
    Rails.logger.warn "Module items not found for course #{course_id}, module #{module_id}, student #{student_id}: #{e.message}"
    nil
  rescue Footrest::HttpError::Unauthorized => e
    Rails.logger.warn "Unauthorized access for course #{course_id}, module #{module_id}, student #{student_id}: #{e.message}"
    nil
  end

  def fetch_pages(course_id)
    canvas_sync_client.get("/api/v1/courses/#{course_id}/pages").all_pages!
  rescue StandardError => e
    Rails.logger.warn "Error fetching WikiPage todo_date for course #{course_id}: #{e.message}"
    nil
  end

  def fetch_topics(course_id)
    canvas_sync_client.get("/api/v1/courses/#{course_id}/discussion_topics").all_pages!
  rescue StandardError => e
    Rails.logger.warn "Error fetching DiscussionTopic todo_date for course #{course_id}: #{e.message}"
    nil
  end
end
