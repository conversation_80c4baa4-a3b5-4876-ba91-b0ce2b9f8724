# frozen_string_literal: true

# #
# AUTO GENERATED LIVE EVENT
# This was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#
# LiveEvent message formats can be found at https://canvas.instructure.com/doc/api/file.live_events.html
# In the general case, LiveEvent message content should not be trusted - it is highly possible that events may
# arrive out of order. Most of the CanvasSync LiveEvent templates solve this by always fetching the object from the API.
#

module LiveEvents
  class ModuleEvent < CanvasSync::LiveEvents::BaseHandler
    def process
      return unless payload['context_type'] == 'Course'

      context_module = ContextModule.find_or_initialize_by(canvas_id: local_canvas_id(payload['module_id']), canvas_context_id: ensure_course.canvas_id, canvas_context_type: 'Course')
      context_module.workflow_state = payload['workflow_state'] if payload['module_item_id'].nil?
      context_module.sync_from_api
    end

    private

    def ensure_course
      course = Course.where(canvas_id: local_canvas_id(local_canvas_id(payload[:context_id]))).first_or_initialize
      return course unless course.new_record?

      course.sync_from_api
      course
    end

    # Live events will use a canvas global ID (cross shard) for any ID's provided. This method will return the local ID.
    def local_canvas_id(id)
      id.to_i % 10_000_000_000_000
    end
  end
end
