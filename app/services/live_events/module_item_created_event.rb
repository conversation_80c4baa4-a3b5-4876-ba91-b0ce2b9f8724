# frozen_string_literal: true

module LiveEvents
  class ModuleItemCreatedEvent < LiveEvents::ModuleItemEvent
    def process
      context_module_item = ContextModuleItem.find_or_initialize_by(
        canvas_id: local_canvas_id(payload[:module_item_id]),
        canvas_context_module_id: local_canvas_id(payload[:module_id])
      )
      # TODO: Sync Workflow state. It is missing from the API response
      context_module_item.sync_from_api
    end
  end
end
