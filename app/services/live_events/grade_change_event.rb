# frozen_string_literal: true

module LiveEvents
  class GradeChangeEvent < LiveEvents::GradeEvent
    def process
      submission = Submission.where(canvas_id: local_canvas_id(payload[:submission_id]),
                                    canvas_course_id: assignment.course.canvas_id,
                                    canvas_assignment_id: assignment.canvas_id,
                                    canvas_user_id: user.canvas_id).first_or_initialize
      submission.points_possible = payload[:points_possible]
      submission.score = payload[:score]
      submission.sync_from_api
    end
  end
end
