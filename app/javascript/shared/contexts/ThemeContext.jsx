import { createContext, useContext, useState, useEffect } from 'react';
import {
  getCourseThemeBackground,
  getThemeElements,
  getThemeColors
} from '@/utils/themeHelper'; // Move logic here

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('no_theme');
  const [themeAssets, setThemeAssets] = useState({
    background: {},
    elements: {},
    colors: {}
  });

  const buildThemeAssets = (theme) => {
    return {
      background: {
        course: getCourseThemeBackground(theme)
      },
      elements: getThemeElements(theme),
      colors: getThemeColors(theme),
    };
  };

  useEffect(() => {
    const theme = window.ENV?.user_config?.theme || 'no_theme';
    setCurrentTheme(theme);
    setThemeAssets(buildThemeAssets(theme));
  }, []);

  const updateTheme = (newTheme) => {
    setCurrentTheme(newTheme);
    setThemeAssets(buildThemeAssets(newTheme));

    // Update the ENV to keep it in sync
    if (window.ENV?.user_config) {
      window.ENV.user_config.theme = newTheme;
    }
  };

  const value = {
    currentTheme,
    themeAssets,
    setCurrentTheme: updateTheme,
    setThemeAssets
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
