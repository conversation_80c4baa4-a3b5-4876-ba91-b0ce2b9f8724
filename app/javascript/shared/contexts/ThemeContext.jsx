import { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('no_theme');

  useEffect(() => {
    const theme = window.ENV?.user_config?.theme || 'no_theme';
    setCurrentTheme(theme);
  }, []);

  const updateTheme = (newTheme) => {
    setCurrentTheme(newTheme);

    // Update the ENV to keep it in sync
    if (window.ENV?.user_config) {
      window.ENV.user_config.theme = newTheme;
    }
  };

  const value = {
    currentTheme,
    setCurrentTheme: updateTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
