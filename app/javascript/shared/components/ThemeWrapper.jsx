import { useTheme } from '../contexts/ThemeContext';

const ThemeWrapper = ({ children, activePage = 'course', enableBackground = true, enableElements = true, className = '', ...props }) => {
  const { currentTheme, themeAssets } = useTheme();
  const backgroundImage = themeAssets?.background?.[activePage];

  const getThemeStyles = () => {
    if (currentTheme === 'no_theme' || !enableBackground) return {};

    return {
      backgroundColor: themeAssets?.colors?.background,
      backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center bottom',
      backgroundSize: 'contain',
      backgroundAttachment: 'scroll',
      minHeight: '100vh',
      width: '100vw',
      margin: 0,
      padding: 0,
      overflowX: 'hidden'
    };
  };

  const getThemeClassName = () => {
    return ['theme-wrapper', `theme-${currentTheme}`, className].filter(Boolean).join(' ');
  };

  return (
    <div
      className={getThemeClassName()}
      style={getThemeStyles()}
      data-theme={currentTheme}
      {...props}
    >
      {children}
    </div>
  );
};

export default ThemeWrapper;
