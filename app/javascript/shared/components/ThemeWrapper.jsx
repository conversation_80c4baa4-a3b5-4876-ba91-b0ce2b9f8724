import { useTheme } from '../contexts/ThemeContext';

const ThemeWrapper = ({ children, activePage = 'course', enableBackground = true, enableElements = true, className = '', ...props }) => {
  const { currentTheme, themeAssets } = useTheme();
  const backgroundImage = themeAssets?.background?.[activePage];

  const getThemeStyles = () => {
    const baseStyles = {
      minHeight: '100vh',
      width: '100vw',
      margin: 0,
      padding: 0,
      overflowX: 'hidden',
      transition: 'background-color 0.3s ease-in-out, background-image 0.3s ease-in-out'
    };

    if (currentTheme === 'no_theme' || !enableBackground) return baseStyles;

    return {
      ...baseStyles,
      backgroundColor: themeAssets?.colors?.background,
      backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center bottom',
      backgroundSize: 'contain',
      backgroundAttachment: 'scroll'
    };
  };

  const getThemeClassName = () => {
    return ['theme-wrapper', `theme-${currentTheme}`, className].filter(Boolean).join(' ');
  };

  return (
    <div
      className={getThemeClassName()}
      style={getThemeStyles()}
      data-theme={currentTheme}
      {...props}
    >
      {children}
    </div>
  );
};

export default ThemeWrapper;
