.ic-Layout-wrapper {
  max-width: 100% !important;
}

.empty-border-box {
  content: "";
  height: 3.5rem;
  width: 0.1rem;
  background: rgb(16, 16, 16);
  display: block;
  margin-left: 5px;
  margin-right: 5px;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #F8F8FF;
    font-family: 'Balsamiq Sans', cursive;
    width: 100%;
    overflow-x: hidden;
}

[data-react-class]
  {height: 100% !important;}

.container-with-bg {
  background-color: #DFEBFB !important;
}


.css-1j1gydm-view-panel__content {
  background-color: transparent !important;
}

.full-window {
  height: 100% !important;
  min-height: 100vh;
  position: absolute;
  left: 0;
  width: 100%;
  background-color: lightblue;
}

/* Smooth theme transitions */
.dashboard-body-container {
  transition: background-color 0.3s ease-in-out;
}

.theme-wrapper * {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

#theme-selection {
  .selected-icon-border {
    border-width: 2px;
    border-style: solid;
    border-color: #1548E6;
    border-radius: 12px;
    display: inline-flex;
    padding: 0;
    margin: 0;
    line-height: 0;
    overflow: hidden;
  }
}

.white-text{
  color: white !important;
  fill: white !important;
}

