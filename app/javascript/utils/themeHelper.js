// Theme asset imports
// Forest
import forestCourseBackground from '../assets/themes/forest/images/background/courses_background.jpg';
import forestAnnouncementBackground from '../assets/themes/forest/images/background/messages_background.jpg';
import forestResourceBackground from '../assets/themes/forest/images/background/resources_background.jpg';

import forestCloud from '../assets/themes/forest/images/elements/cloud.png';
import forestCloud2 from '../assets/themes/forest/images/elements/cloud2.png';
import forestCloud3 from '../assets/themes/forest/images/elements/cloud3.png';
import forestSun from '../assets/themes/forest/images/elements/sun.png';

// Winter
import winterCourseBackground from '../assets/themes/winter/images/background/courses_background.jpg';
import winterAnnouncementBackground from '../assets/themes/winter/images/background/messages_background.jpg';
import winterResourceBackground from '../assets/themes/winter/images/background/resources_background.jpg';

// Space
import spaceCourseBackground from '../assets/themes/space/images/background/courses_background.jpg';
import spaceAnnouncementBackground from '../assets/themes/space/images/background/messages_background.jpg';
import spaceResourceBackground from '../assets/themes/space/images/background/resources_background.jpg';


export const getCourseThemeBackground = (theme) => {
  switch (theme) {
    case 'forest':
      return forestCourseBackground;
    case 'winter':
      return winterCourseBackground;
    case 'space':
      return spaceCourseBackground;
    default:
      return null;
  }
};

export const getAnnouncementThemeBackground = (theme) => {
  switch (theme) {
    case 'forest':
      return forestAnnouncementBackground;
    case 'winter':
      return winterAnnouncementBackground;
    case 'space':
      return spaceAnnouncementBackground;
    default:
      return null;
  }
};

export const getResourceThemeBackground = (theme) => {
  switch (theme) {
    case 'forest':
      return forestResourceBackground;
    case 'winter':
      return winterResourceBackground;
    case 'space':
      return spaceResourceBackground;
    default:
      return null;
  }
};

export const getThemeElements = (theme) => {
  switch (theme) {
    case 'forest':
      return {
        cloud: forestCloud,
        cloud2: forestCloud2,
        cloud3: forestCloud3,
        sun: forestSun,
      };
    case 'winter':
      return {}; // Add winter theme elements later
    case 'space':
      return {}; // Add space theme elements later
    default:
      return {};
  }
};

export const getThemeColors = (theme) => {
  switch (theme) {
    case 'forest':
      return {
        primary: '#2D5016',
        secondary: '#4A7C59',
        accent: '#8FBC8F',
        background: '#CBEFFD',
      };
    case 'winter':
      return {
        primary: '#1E3A8A',
        secondary: '#3B82F6',
        accent: '#93C5FD',
        background: '#73C2EF',
      };
    case 'space':
      return {
        primary: '#4C1D95',
        secondary: '#7C3AED',
        accent: '#C4B5FD',
        background: '#071933',
      };
      case 'no_theme':
        return {
          primary: '#4C1D95',
          secondary: '#7C3AED',
          accent: '#C4B5FD',
          background: '#F8F8FF',
        };
    default:
      return {
        primary: '#374151',
        secondary: '#6B7280',
        accent: '#9CA3AF',
        background: '#F9FAFB',
      };
  }
};
