import '@/application.css'
import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { IconUserLine,
         IconSettingsLine } from '@instructure/ui-icons';
import { Link } from '@instructure/ui-link';
import { Spinner } from '@instructure/ui-spinner';

import * as API from "../utils/api";
import DashboardPageWrapper from './DashboardPageWrapper';
import StudentSelect from '../shared/components/StudentSelect';
import UserConfigModal from './learning_coach_dashboard/UserConfigModal';
import DashboardBody from './student_dashboard/DashboardBody';
import CourseProgress from './courses/CourseProgress';

const LearningCoachDashboard = () => {
  const currentUserID = window.ENV.user.canvas_id;
  const [students, setStudents] = useState([]);
  const [isStudentsLoading, setIsStudentsLoading] = useState(true);
  const [selectedStudentId, setSelectedStudentId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  let initialStudentId;

  const toggleModal = (e) => {
    setIsModalOpen(!isModalOpen);
  };

  const redirectToCanvas = (studentId) => {
    let url = (window.location != window.parent.location)
          ? document.referrer
          : document.location.href;

    window.parent.location = `${url}?student_id=${studentId}`;
  };

  // Handle parameters from Parent Window (Canvas Query Params) inside iFrame (LTI)
  useEffect(() => {
    const handleMessage = (event) => {
      if (initialStudentId || event.data.action != "setLTIParams") return
      console.log("Received message from parent window:", event.data);

      const params = new URLSearchParams(event.data.data);
      const student_id = params.get("student_id");

      initialStudentId = student_id;

      getStudentList();
    };

    window.addEventListener("message", handleMessage);

    return () => window.removeEventListener("message", handleMessage)
  }, []);

  // Redirect to Canvas if student is not K5 Student
  useEffect(() => {
    if (selectedStudentId) {
      setIsStudentsLoading(true);

      let student = students.find((o) => o.id === selectedStudentId);
      if (student && !student.is_k5_student) {
        redirectToCanvas(selectedStudentId);
      } else {
        // Student is K5 Student, so hide loader
        setIsStudentsLoading(false);
      }
    }
  }, [selectedStudentId]);

  const getStudentList = () => {
    const canvas_user_locale = window.ENV.canvas_user_locale;
    const canvas_user_timezone = window.ENV.canvas_user_timezone;

    API.getK5Students(currentUserID)
      .then((response) => response.data)
      .then((response) => {

        const options = response.map(obj => {
          return {'id': obj.canvas_id.toString(), 'name': obj.sortable_name, 'is_k5_student': obj.is_k5_student}
        })
        setStudents(options);

        // Set selected student based on initial student id
        let preSelectedStudentId = initialStudentId;
        if (preSelectedStudentId) {
          let student = options.find((o) => o.id === preSelectedStudentId)
          preSelectedStudentId = student ? preSelectedStudentId : options[0].id;
        }
        preSelectedStudentId = preSelectedStudentId ? preSelectedStudentId : options[0].id;
        setSelectedStudentId(preSelectedStudentId);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderStudentDashboard = () => {
    return <DashboardBody
              userId={selectedStudentId}
            />
  }

  const renderCourseProgress = () => {
    return (
      <CourseProgress studentId={selectedStudentId} courseId={window.ENV.canvas_course_id}/>
    )
  }

  const renderPageBody = () => {
    if (window.ENV.context === 'course') {
      return renderCourseProgress();
    }else {
      return renderStudentDashboard();
    }
  }

  // Show loader while students are loading
  if (isStudentsLoading) {
    return (
      <Flex justifyItems="center" alignItems="center" height="100%">
        <Spinner renderTitle="Loading student information..." size="large" />
      </Flex>
    );
  }

  return (
    <View as="div">
      <View as="div" textAlign="end">
        <Flex margin="none none large">
          <Flex.Item shouldShrink shouldGrow></Flex.Item>
          <Flex.Item padding="x-small">
            { students && !isStudentsLoading && (
              <StudentSelect
                label=""
                options={students}
                inputValue={students.find((o) => o.id === selectedStudentId).name}
                selectedOptionId={selectedStudentId}
                onChange={(optionID) => setSelectedStudentId(optionID)}
              />
            )}
          </Flex.Item>
          <Flex.Item padding="x-small">|</Flex.Item>
          <Flex.Item padding="x-small">
            <Link
              renderIcon={<IconSettingsLine />}
              onClick={toggleModal}
            >Settings</Link>
          </Flex.Item>
        </Flex>
      </View>
      <View as="div">
        { selectedStudentId && renderPageBody() }
      </View>
      <UserConfigModal
        key={`userConfigModal-2`}
        isModalOpen={isModalOpen}
        toggleModal={toggleModal}
        userId={selectedStudentId}
      />
    </View>
  )
}

export default LearningCoachDashboard
