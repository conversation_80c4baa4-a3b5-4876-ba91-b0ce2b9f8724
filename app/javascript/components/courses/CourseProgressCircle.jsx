import { useEffect, useState } from 'react';

import { ProgressCircle } from '@instructure/ui-progress';
import { Popover } from '@instructure/ui-popover';
import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';

const CourseProgressCircle = (props) => {
  const { course } = props;
  const [showingProgress, setShowingProgress] = useState(false);

  const renderProgressPercentText = () => {
    return (
      <View as="div" background="transparent" borderWidth="none">
        <View as="div"><Text size="large" weight="bold">{course.course_progress_percent} %</Text></View>
        <View as="div"><Text color="brand" size="small">Completion</Text></View>
      </View>
    )
  }

  const renderProgress = () => {
    return (
      <Popover
        renderTrigger={ renderProgressPercentText() }
        isShowingContent={showingProgress}
        on="click"
        onShowContent={(e) => {setShowingProgress(true)}}
        onHideContent={(e, {documentClick}) => {setShowingProgress(false)}}
        placement='bottom end'
        shouldCloseOnDocumentClick={true}
        shouldContainFocus={false}
        shouldFocusContentOnTriggerBlur={true}
      >
        <View display="block" width="16rem">
          <View
            as="div"
            padding="small"
            borderWidth="small"
            themeOverride={{
              borderWidthSmall: '0.05rem'
            }}
          >
            <Text size="small" weight="bold">Course Completion Progress</Text>
          </View>
          <View
            as="div"
            borderWidth="small"
            themeOverride={{
              borderWidthSmall: '0.05rem'
            }}
          >
            <View as="div">
              <View textAlign="end" margin="x-small" display="inline-block" width="75%">
                <Text size="small">Overall Course Progress:</Text>
              </View>
              <View textAlign="end" width="15%" display="inline-block"><Text size="small">{course.course_progress_percent}%</Text></View>
            </View>
            <View as="div">
              <View textAlign="end" margin="none x-small" display="inline-block" width="75%">
                <Text size="small">Expected Course Progress:</Text>
              </View>
              <View textAlign="end" width="15%" display="inline-block"><Text size="small">{course.expected_course_progress}%</Text></View>
            </View>
            <View as="div">
              <View textAlign="end" margin="x-small" display="inline-block" width="75%">
                <Text size="small">Percentage of Expected Course Progress:</Text>
              </View>
              <View textAlign="end" width="15%" display="inline-block"><Text size="small">{course.expected_course_progress_percent}%</Text></View>
            </View>
          </View>
        </View>
      </Popover>
    )
  }

  return (
    <ProgressCircle
      valueNow={course.requirement_completed_count}
      valueMax={course.requirement_count}
      screenReaderLabel='Course Progress'
      renderValue={() => { return renderProgress() }}
      margin="0 0 small"
      size="large"
      themeOverride={{
        trackColor: '#2d3b4514'
      }}
      onClick={() => setShowingProgress(true) }
    />
  )
}

export default CourseProgressCircle