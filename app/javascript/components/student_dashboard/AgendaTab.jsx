import { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, Routes } from 'react-router-dom'

import { Tabs } from '@instructure/ui-tabs';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Heading } from '@instructure/ui-heading';
import { Flex } from '@instructure/ui-flex';
import moment from 'moment-timezone';
import DayView from '../agenda/DayView';
import WeekView from '../agenda/WeekView';
import PastDues from '../agenda/PastDues';
import * as API from "../../utils/api";
import { TabTitle, TextWithBadgeCount } from '../../shared/components/UtilUI';

const AgendaTab = (props) => {
  const { studentId } = props;
  const [selectedTab, setSelectedTab] = useState(0);
  const [assignments, setAssignments] = useState([]);

  const handleTabChange = (data) => {
    setSelectedTab(data.id)
  }

  useEffect(() => {
    getDayDueAssignments();
  }, [studentId]);

  const getDayDueAssignments = () => {
    API.getStudentDayDueAssignments(studentId, {only_past_dues: true})
      .then((response) => response.data)
      .then((response) => {
        setAssignments(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderTitle = () => {
    if (selectedTab === 'past_due_view') {
      return <TabTitle title='Past Due' />
    } else {
      return <TextWithBadgeCount title='Past Due' count={assignments.length} />
    }
  }

  const renderAgendaHeader = () => {
    // Use moment-timezone to format the date in the user's timezone
    const formattedDate = moment().tz(window.ENV.user_config?.timezone ?? 'UTC').format('dddd, MMMM D, YYYY');

    return (
      <View as="div" padding="small">
        <Flex justifyItems="space-between" alignItems="center">
          <Flex.Item>
            <Heading level="h2" margin="0">
              <Text size="x-large" weight="bold">My Agenda</Text>
            </Heading>
          </Flex.Item>
          <Flex.Item>
            <Text size="medium" weight="normal">
              <strong>Today's Date:</strong> {formattedDate}
            </Text>
          </Flex.Item>
        </Flex>
      </View>
    );
  }

  return (
    <View
      as='div'
      height='100%'
      width='100%'
    >
      {renderAgendaHeader()}
      <Tabs
        variant="secondary"
        margin='small 0'
        padding='0'
        onRequestTabChange={(event, data) => handleTabChange(data)}
      >
        <Tabs.Panel
          key='day_view'
          id='day_view'
          renderTitle={<TabTitle title='Daily Plan' />}
          textAlign='center'
          padding='xx-small 0 0 0'
          isSelected={selectedTab === 'day_view'}
        >
          <DayView studentId={studentId} />
        </Tabs.Panel>
        <Tabs.Panel
          key='week_view'
          id='week_view'
          renderTitle={<TabTitle title='Weekly Plan' />}
          textAlign='center'
          padding='xx-small 0 0 0'
          isSelected={selectedTab === 'week_view'}
        >
          <WeekView studentId={studentId} />
        </Tabs.Panel>
        <Tabs.Panel
          key='past_due_view'
          id='past_due_view'
          renderTitle={renderTitle}
          textAlign='center'
          padding='xx-small 0 0 0'
          isSelected={selectedTab === 'past_due_view'}
        >
          <PastDues studentId={studentId} dueAssignments={assignments}/>
        </Tabs.Panel>
      </Tabs>
    </View>
  )
}

export default AgendaTab
