# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_07_17_081104) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "account_grading_scheme_colors", force: :cascade do |t|
    t.bigint "canvas_account_id", null: false
    t.integer "scheme_color_type", null: false
    t.string "name"
    t.string "range_value"
    t.string "color_code"
    t.string "default_color_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "accounts", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "sis_id"
    t.bigint "canvas_parent_account_id"
    t.string "sis_parent_account_id"
    t.string "name"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_id"], name: "index_accounts_on_canvas_id", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "assignments", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "title"
    t.text "description"
    t.datetime "due_at", precision: nil
    t.datetime "unlock_at", precision: nil
    t.datetime "lock_at", precision: nil
    t.float "points_possible"
    t.float "min_score"
    t.float "max_score"
    t.float "mastery_score"
    t.string "grading_type"
    t.string "submission_types"
    t.string "workflow_state"
    t.integer "canvas_context_id"
    t.string "canvas_context_type"
    t.integer "canvas_assignment_group_id"
    t.integer "canvas_grading_scheme_id"
    t.integer "canvas_grading_standard_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_context_id", "canvas_context_type"], name: "index_assignments_on_canvas_context_id_and_canvas_context_type"
    t.index ["canvas_id"], name: "index_assignments_on_canvas_id", unique: true
  end

  create_table "calendar_events", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_user_id", null: false
    t.string "title"
    t.datetime "start_at"
    t.datetime "end_at"
    t.string "html_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "completed_at"
    t.string "context_code"
    t.index ["canvas_id", "canvas_user_id"], name: "index_calendar_events_on_canvas_id_and_canvas_user_id", unique: true
    t.index ["canvas_user_id"], name: "index_calendar_events_on_canvas_user_id"
    t.index ["start_at"], name: "index_calendar_events_on_start_at"
  end

  create_table "canvas_sync_job_logs", id: :serial, force: :cascade do |t|
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.string "exception"
    t.text "backtrace"
    t.string "job_class"
    t.string "status"
    t.text "metadata"
    t.text "job_arguments"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "job_id"
    t.integer "fork_count"
    t.index ["job_id"], name: "index_canvas_sync_job_logs_on_job_id"
  end

  create_table "canvas_sync_sync_batches", id: :serial, force: :cascade do |t|
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.string "status"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.boolean "full_sync", default: false
    t.string "batch_genre"
    t.string "batch_bid"
  end

  create_table "completed_calendar_events", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "context_module_items", force: :cascade do |t|
    t.bigint "canvas_id"
    t.bigint "canvas_context_module_id"
    t.integer "position"
    t.bigint "canvas_content_id"
    t.string "canvas_content_type"
    t.bigint "canvas_assignment_id"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_context_module_id"], name: "index_context_module_items_on_canvas_context_module_id"
    t.index ["canvas_id"], name: "index_context_module_items_on_canvas_id", unique: true
  end

  create_table "context_module_progressions", force: :cascade do |t|
    t.bigint "canvas_module_id", null: false
    t.bigint "canvas_module_item_id", null: false
    t.bigint "canvas_user_id", null: false
    t.string "canvas_content_type"
    t.bigint "canvas_content_id"
    t.string "canvas_content_title"
    t.string "canvas_page_url"
    t.string "requirement_type"
    t.string "requirement_status"
    t.datetime "lock_at"
    t.datetime "due_at"
    t.datetime "todo_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["canvas_module_id", "canvas_module_item_id", "canvas_user_id"], name: "by_canvas_module_id_module_item_id_canvas_user_id", unique: true
  end

  create_table "context_modules", force: :cascade do |t|
    t.bigint "canvas_id"
    t.bigint "canvas_context_id"
    t.string "canvas_context_type"
    t.integer "position"
    t.string "name"
    t.string "workflow_state"
    t.datetime "deleted_at", precision: nil
    t.datetime "unlock_at", precision: nil
    t.jsonb "prerequisites"
    t.jsonb "completion_requirements"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_context_id", "canvas_context_type"], name: "index_context_modules_on_context"
    t.index ["canvas_id"], name: "index_context_modules_on_canvas_id", unique: true
  end

  create_table "course_progresses", force: :cascade do |t|
    t.bigint "canvas_course_id", null: false
    t.bigint "canvas_user_id", null: false
    t.integer "requirement_count"
    t.integer "requirement_completed_count"
    t.datetime "completion_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["canvas_course_id"], name: "index_course_progresses_on_canvas_course_id"
    t.index ["canvas_user_id", "canvas_course_id"], name: "index_course_progresses_on_canvas_user_id_and_canvas_course_id", unique: true
    t.index ["canvas_user_id"], name: "index_course_progresses_on_canvas_user_id"
  end

  create_table "courses", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "sis_id"
    t.string "course_code"
    t.string "name"
    t.string "workflow_state"
    t.integer "canvas_account_id"
    t.integer "canvas_term_id"
    t.datetime "start_at", precision: nil
    t.datetime "end_at", precision: nil
    t.bigint "grading_standard_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "image_url"
    t.string "grade_level"
    t.index ["canvas_id"], name: "index_courses_on_canvas_id", unique: true
  end

  create_table "enrollments", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_course_id"
    t.string "course_sis_id"
    t.bigint "canvas_user_id"
    t.string "user_sis_id"
    t.string "role"
    t.bigint "canvas_role_id"
    t.bigint "canvas_section_id"
    t.string "workflow_state"
    t.string "base_role_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "canvas_associated_user_id"
    t.datetime "canvas_created_at"
    t.datetime "canvas_start_at"
    t.index ["canvas_course_id"], name: "index_enrollments_on_canvas_course_id"
    t.index ["canvas_id"], name: "index_enrollments_on_canvas_id", unique: true
    t.index ["canvas_user_id"], name: "index_enrollments_on_canvas_user_id"
  end

  create_table "exception_dates", force: :cascade do |t|
    t.date "date"
    t.string "name"
    t.integer "exception_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "external_resources", force: :cascade do |t|
    t.string "base_url"
    t.string "description"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "global_settings", force: :cascade do |t|
    t.integer "setting_type", null: false
    t.jsonb "settings", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["setting_type"], name: "index_global_settings_on_setting_type"
  end

  create_table "grading_schemes", force: :cascade do |t|
    t.bigint "canvas_course_id", null: false
    t.text "data"
    t.integer "scheme_color_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "inst_data_shipper_dump_batches", id: :serial, force: :cascade do |t|
    t.datetime "started_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.string "status"
    t.string "job_class"
    t.string "genre"
    t.string "batch_id"
    t.string "exception"
    t.text "backtrace"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "panda_pal_api_calls", id: :serial, force: :cascade do |t|
    t.text "logic"
    t.string "expiration"
    t.integer "uses_remaining"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "panda_pal_organizations", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "key"
    t.string "secret"
    t.string "canvas_account_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "salesforce_id"
    t.text "encrypted_settings"
    t.string "encrypted_settings_iv"
    t.string "shard"
    t.integer "canvas_shard_id"
    t.index ["key"], name: "index_panda_pal_organizations_on_key", unique: true
    t.index ["name"], name: "index_panda_pal_organizations_on_name", unique: true
  end

  create_table "panda_pal_sessions", id: :serial, force: :cascade do |t|
    t.string "session_key"
    t.text "data"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "panda_pal_organization_id"
    t.index ["panda_pal_organization_id"], name: "index_panda_pal_sessions_on_panda_pal_organization_id"
    t.index ["session_key"], name: "index_panda_pal_sessions_on_session_key", unique: true
  end

  create_table "pseudonyms", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_user_id"
    t.string "sis_id"
    t.string "unique_id"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_id"], name: "index_pseudonyms_on_canvas_id", unique: true
    t.index ["canvas_user_id"], name: "index_pseudonyms_on_canvas_user_id"
  end

  create_table "roles", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "label"
    t.string "base_role_type"
    t.integer "canvas_account_id"
    t.string "workflow_state"
    t.json "permissions"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_account_id"], name: "index_roles_on_canvas_account_id"
    t.index ["canvas_id"], name: "index_roles_on_canvas_id", unique: true
  end

  create_table "scores", force: :cascade do |t|
    t.bigint "canvas_enrollment_id", null: false
    t.float "current_score"
    t.float "unposted_current_score"
    t.float "final_score"
    t.string "current_letter_grade"
    t.string "unposted_letter_grade"
    t.string "final_letter_grade"
    t.string "override_score"
    t.string "workflow_state", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["canvas_enrollment_id"], name: "index_scores_on_canvas_enrollment_id", unique: true
  end

  create_table "submissions", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.bigint "canvas_course_id"
    t.bigint "canvas_assignment_id"
    t.bigint "canvas_user_id"
    t.datetime "submitted_at", precision: nil
    t.datetime "due_at", precision: nil
    t.datetime "graded_at", precision: nil
    t.float "score"
    t.float "points_possible"
    t.boolean "excused"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_assignment_id"], name: "index_submissions_on_canvas_assignment_id"
    t.index ["canvas_course_id"], name: "index_submissions_on_canvas_course_id"
    t.index ["canvas_id"], name: "index_submissions_on_canvas_id", unique: true
    t.index ["canvas_user_id"], name: "index_submissions_on_canvas_user_id"
  end

  create_table "terms", force: :cascade do |t|
    t.integer "canvas_id", null: false
    t.string "name"
    t.datetime "start_at", precision: nil
    t.datetime "end_at", precision: nil
    t.string "workflow_state"
    t.integer "grading_period_group_id"
    t.string "sis_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["canvas_id"], name: "index_terms_on_canvas_id", unique: true
  end

  create_table "user_configs", force: :cascade do |t|
    t.bigint "canvas_user_id"
    t.string "language"
    t.integer "home_page"
    t.string "timezone"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "show_notification_tooltip", default: true
    t.integer "theme", default: 1, null: false
    t.boolean "audio_enabled", default: true, null: false
  end

  create_table "user_observers", force: :cascade do |t|
    t.bigint "observing_user_id"
    t.bigint "observed_user_id"
    t.string "workflow_state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["observed_user_id", "observing_user_id"], name: "index_user_observers_on_observed_user_id_and_observing_user_id", unique: true
    t.index ["observed_user_id"], name: "index_user_observers_on_observed_user_id"
    t.index ["observing_user_id"], name: "index_user_observers_on_observing_user_id"
  end

  create_table "user_shard_associations", force: :cascade do |t|
    t.bigint "canvas_user_id"
    t.integer "canvas_shard_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["canvas_user_id", "canvas_shard_id"], name: "shard_assoc_uniqueness", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.bigint "canvas_id", null: false
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.string "workflow_state"
    t.string "login_id"
    t.string "name"
    t.string "sortable_name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "canvas_role_id"
    t.string "grade_level"
    t.index ["canvas_id"], name: "index_users_on_canvas_id", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
end
